package com.mediacomm.caesar.util.mapper;

import com.mediacomm.caesar.domain.CaesarDeviceSameField;
import com.mediacomm.caesar.domain.CaesarPanelRect;
import com.mediacomm.caesar.domain.CaesarProperty;
import com.mediacomm.caesar.domain.CaesarRx;
import com.mediacomm.caesar.domain.CaesarSeat;
import com.mediacomm.caesar.domain.CaesarServer;
import com.mediacomm.caesar.domain.CaesarTx;
import com.mediacomm.caesar.domain.CaesarUser;
import com.mediacomm.caesar.domain.CaesarVideoWall;
import com.mediacomm.caesar.domain.Slot;
import com.mediacomm.caesar.domain.kaito.KaitoGroupData;
import com.mediacomm.caesar.domain.kaito.KaitoInputData;
import com.mediacomm.caesar.domain.kaito.KaitoIpcData;
import com.mediacomm.caesar.domain.kaito.KaitoVideoWall;
import com.mediacomm.caesar.util.InspectCaesarUtil;
import com.mediacomm.caesar.util.mapper.annotations.MapIntToString;
import com.mediacomm.caesar.util.mapper.annotations.ToCaesarVideoSrcId;
import com.mediacomm.caesar.util.mapper.annotations.ToSkylinkVideoSrcId;
import com.mediacomm.caesar.util.mapper.annotations.ToSkylinkVideoSrcName;
import com.mediacomm.caesar.util.mapper.jpa.KvmAssetContext;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmSeat;
import com.mediacomm.entity.dao.KvmSlot;
import com.mediacomm.entity.dao.KvmUser;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.KvmVideoWallDecoder;
import com.mediacomm.entity.dao.Version;
import com.mediacomm.entity.message.LayerData;
import com.mediacomm.entity.message.PanelRect;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.sysenum.BannerType;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.LayerStatusType;
import com.mediacomm.system.variable.sysenum.LayoutType;
import com.mediacomm.util.SkyLinkStringUtil;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.BeforeMapping;
import org.mapstruct.Context;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * CaesarEntityMapperResolver.
 */
@Component
@Slf4j
public class CaesarEntityMapperResolver {

  @Autowired
  private KvmAssetService kvmAssetService;

  @MapIntToString
  public String mapIntToString(int integer) {
    return String.valueOf(integer);
  }

  /**
   * 凯撒设备ID转云视外设ID.
   */
  @ToSkylinkVideoSrcId
  public String toSkylinkVideoSrcId(int caesarVideoSrcId, @Context KvmAssetContext context) {
    lookupKvmAsset(caesarVideoSrcId, context);
    KvmAsset kvmAsset = context.getKvmAsset();
    return kvmAsset == null ? "" : kvmAsset.getAssetId();
  }

  /**
   * 根据凯撒设备ID查设备名称.
   */
  @ToSkylinkVideoSrcName
  public String toSkylinkVideoSrcName(int caesarVideoSrcId, @Context KvmAssetContext context) {
    lookupKvmAsset(caesarVideoSrcId, context);
    KvmAsset kvmAsset = context.getKvmAsset();
    return kvmAsset == null ? "" : kvmAsset.getName();
  }

  /**
   * 云视ID转凯撒设备ID.
   */
  @ToCaesarVideoSrcId
  public int toCaesarVideoSrcId(String assetId, @Context KvmAssetContext context) {
    lookupKvmAsset(assetId, context);
    KvmAsset kvmAsset = context.getKvmAsset();
    return kvmAsset == null ? 0 : kvmAsset.getDeviceId();
  }

  /**
   * buildProperties.
   */
  @BeforeMapping
  public void toKvmAssetBefore(@NotNull CaesarTx caesarTx, @MappingTarget KvmAsset kvmAsset,
                               @Context String masterId) {
    initializeCommonAssetData(kvmAsset, caesarTx, masterId);
    kvmAsset.setDeviceModel(DeviceType.CAESAR_TX.getDeviceTypeId());
  }

  /**
   * buildProperties.
   */
  @BeforeMapping
  public void toKvmAssetBefore(@NotNull CaesarRx caesarRx, @MappingTarget KvmAsset kvmAsset,
                               @Context String masterId) {
    initializeCommonAssetData(kvmAsset, caesarRx, masterId);
    kvmAsset.getProperties().add(new Property("rxType",
        String.valueOf(caesarRx.getRxType())));
    kvmAsset.setDeviceModel(DeviceType.CAESAR_RX.getDeviceTypeId());
    processAndSetSpecialRxDeviceModel(kvmAsset, caesarRx);
  }

  /**
   * .
   */
  @BeforeMapping
  public void toKvmAssetBefore(@NotNull KaitoInputData input, @MappingTarget KvmAsset kvmAsset,
                               @Context String masterId, @Context Integer groupId) {
    kvmAsset.setAssetId(SkyLinkStringUtil.uuid());
    kvmAsset.setMasterId(masterId);
    kvmAsset.setDeviceId(input.getInputId());
    kvmAsset.setDeviceModel(DeviceType.KAITO02_INPUT.getDeviceTypeId());
    kvmAsset.setHardcode(String.format("%s.%d.input.%d", masterId, groupId, input.getInputId()));
  }

  /**
   * .
   */
  @BeforeMapping
  public void toKvmAssetBefore(@NotNull KaitoIpcData ipc, @MappingTarget KvmAsset kvmAsset,
                               @Context String masterId, @Context Integer groupId) {
    kvmAsset.setAssetId(SkyLinkStringUtil.uuid());
    kvmAsset.setMasterId(masterId);
    kvmAsset.setDeviceId(ipc.getSourceId());
    kvmAsset.setDeviceModel(DeviceType.KAITO02_IPC.getDeviceTypeId());
    kvmAsset.setHardcode(String.format("%s.%d.ipc.%d", masterId, groupId, ipc.getSourceId()));
    for (KaitoIpcData.Stream stream : ipc.getStreams()) {
      switch (stream.getStreamIndex()) {
        case 0 -> kvmAsset.getProperties().add(new Property("mainStreamId",
            String.valueOf(stream.getStreamId())));
        case 1 -> kvmAsset.getProperties().add(new Property("subStreamId",
            String.valueOf(stream.getStreamId())));
        default -> log.warn("Undefine kaito stream index:" + stream.getStreamIndex());
      }
    }
  }

  @BeforeMapping
  public void toPanelRectBefore(@NotNull CaesarPanelRect caesarPanelRect,
                                @MappingTarget PanelRect panelRect,
                                @Context String masterId) {
    KvmAssetVo kvmAsset = kvmAssetService.oneByDeviceId(caesarPanelRect.getVideoSrcId(), masterId);
    if (kvmAsset != null) {
      panelRect.addFullScreenLayer(kvmAsset, caesarPanelRect.getCtrlMode());
      LayerData layer = panelRect.getLayerData(0);
      if (layer != null) {
        layer.setEnableAudio(caesarPanelRect.isEnableAudio());
      }
    }
  }

  /**
   * .
   */
  @BeforeMapping
  public void toPanelRectBefore(@NotNull KaitoVideoWall.Layer kaitoLayer,
                                @MappingTarget PanelRect panelRect,
                                @Context KvmVideoWall videoWall) {
    panelRect.setPanelId(kaitoLayer.getId());
    panelRect.setSeq(kaitoLayer.getZorder());
    int width = kaitoLayer.getWindow().getWidth();
    int height = kaitoLayer.getWindow().getHeight();
    int x = kaitoLayer.getWindow().getX();
    int y = kaitoLayer.getWindow().getY();
    panelRect.setXpos(x);
    panelRect.setYpos(y);
    panelRect.setWidth(width);
    panelRect.setHeight(height);
    panelRect.setXpos(x);
    panelRect.setStatus(
        kaitoLayer.getStatus() == 0 ? LayerStatusType.NORMAL : LayerStatusType.NO_RESOURCE);
    int groupId = Integer.parseInt(Property.findValueByKey(
        videoWall.getProperties(), "groupId", "0"));
    List<KaitoVideoWall.Source> sources = kaitoLayer.getSource().stream().toList();
    List<KvmAssetVo> assets = new ArrayList<>();
    for (int i = 0; i < sources.size(); i++) {
      KaitoVideoWall.Source source = sources.get(i);
      KvmAssetVo kvmAsset;
      if (source.getSourceType() == 0) { // 无源
        continue;
      } else if (source.getSourceType() == 1) { // 诺瓦输入卡
        kvmAsset = kvmAssetService.oneByHardcode(
            InspectCaesarUtil.getKaitoDeviceHardCode(videoWall.getMasterId(), groupId,
                "input", source.getSourceId()));
      } else if (source.getSourceType() == 2) { // ipc
        kvmAsset = kvmAssetService.oneByHardcode(
            InspectCaesarUtil.getKaitoDeviceHardCode(videoWall.getMasterId(), groupId,
                "ipc", source.getSourceId()));
      } else { // Tx
        kvmAsset = kvmAssetService.oneByDeviceId(source.getSourceId(), videoWall.getMasterId());
      }
      assets.add(kvmAsset);
    }
    if (!assets.isEmpty()) {
      if (kaitoLayer.getType() == 2) { // 四宫格
        panelRect.addFourScreenLayers(assets);
      } else {
        panelRect.addFullScreenLayer(assets.getFirst(), 2);
      }
    }
  }

  /**
   * .
   */
  @BeforeMapping
  public void fromPanelRectBefore(@NotNull PanelRect panelRect,
                                  @MappingTarget KaitoVideoWall.Layer layer) {
    KaitoVideoWall.Window window = new KaitoVideoWall.Window();
    window.setX(panelRect.getXpos());
    window.setY(panelRect.getYpos());
    window.setWidth(panelRect.getWidth());
    window.setHeight(panelRect.getHeight());
    layer.setWindow(window);
    layer.setId(panelRect.getPanelId());
    layer.setZorder(panelRect.getSeq());
    Collection<KaitoVideoWall.Source> sources = new ArrayList<>();
    layer.setSource(sources);
    LayoutType layoutType = panelRect.getLayoutType();
    if (layoutType == LayoutType.FULL_SCREEN) {
      LayerData panel = panelRect.getChildPanels().stream().findFirst().orElse(null);
      if (panel != null) {
        KvmAsset asset = kvmAssetService.getById(panel.getVideoSrcId());
        if (Objects.equals(asset.getDeviceModel(), DeviceType.CAESAR_TX.getDeviceTypeId())) {
          layer.setType(1); // Tx全屏
        } else {
          layer.setType(0); // 普通信号图层
        }
        sources.add(InspectCaesarUtil.buildKaitoSource(asset));
      }
    } else if (layoutType == LayoutType.FOUR_SCREEN) { // 四宫格只有Tx
      layer.setType(2);
      for (LayerData childPanel : panelRect.getChildPanels()) {
        KvmAsset asset = kvmAssetService.getById(childPanel.getVideoSrcId());
        KaitoVideoWall.Source source = new KaitoVideoWall.Source();
        sources.add(source);
        if (Objects.equals(asset.getDeviceModel(), DeviceType.CAESAR_TX.getDeviceTypeId())) {
          source.setSourceId(asset.getDeviceId());
          source.setSourceType(3);
        } else {
          source.setSourceId(0); // 无源
        }
      }
    }
  }

  /**
   * .
   */
  @BeforeMapping
  public void toKvmSlotBefore(@NotNull Slot caesarSlot, @MappingTarget KvmSlot kvmSlot,
                              @Context String masterId) {
    kvmSlot.setMasterId(masterId);
    kvmSlot.setName(String.format("slot.%s", caesarSlot.getSlotNumber()));
    kvmSlot.setDeviceModel(DeviceType.CAESAR_SLOT.getDeviceTypeId());
    kvmSlot.getProperties().add(new Property("type", caesarSlot.getType()));
    kvmSlot.getProperties().add(new Property("portNumber",
        String.valueOf(caesarSlot.getPortNumber())));
  }

  /**
   * .
   */
  @BeforeMapping
  public void toKvmVideoWallBefore(CaesarVideoWall caesarVideoWall,
                                   @MappingTarget KvmVideoWall kvmVideoWall,
                                   @Context String masterId) {
    kvmVideoWall.setMasterId(masterId);
    kvmVideoWall.setDeviceModel(caesarVideoWall.getDeviceType().getDeviceTypeId());
    kvmVideoWall.setDeviceType(caesarVideoWall.getDeviceType());
    kvmVideoWall.setModelName(caesarVideoWall.getDeviceType().getDeviceTypeTitle());
    kvmVideoWall.setBannerType(caesarVideoWall.getBannerType());
    kvmVideoWall.setUniqueSearchKey(InspectCaesarUtil.getCaesarWallUniqueSearchKey(masterId, caesarVideoWall));
    Property supportAudio = Property.findFromArray(PropertyKeyConst.SUPPORT_AUDIO, kvmVideoWall.getProperties());
    String propertyValue = String.valueOf(caesarVideoWall.isSupportAudio());
    if (supportAudio == null) {
      kvmVideoWall.getProperties().add(new Property(PropertyKeyConst.SUPPORT_AUDIO, propertyValue));
    } else {
      supportAudio.setPropertyValue(propertyValue);
    }
  }

  /**
   * .
   */
  @BeforeMapping
  public void toKvmVideoWallBefore(KaitoVideoWall kaitoVideoWall,
                                   @MappingTarget KvmVideoWall kvmVideoWall,
                                   @Context KaitoGroupData groupData,
                                   @Context String masterId) {
    kvmVideoWall.setMasterId(masterId);
    String groupId = String.valueOf(groupData.getId());
    kvmVideoWall.setUniqueSearchKey(String.join(".", masterId, "caesar-kaito", groupId,
            String.valueOf(kaitoVideoWall.getId())));
    kvmVideoWall.getProperties().add(new Property("groupId", groupId));
    kvmVideoWall.getProperties().add(new Property(PropertyKeyConst.ADDRESS,
        String.valueOf(groupData.getIp())));
    kvmVideoWall.getProperties().add(new Property(PropertyKeyConst.PORT,
        String.valueOf(groupData.getPort())));
    kvmVideoWall.getProperties().add(new Property(PropertyKeyConst.SECRETE_KEY,
        String.valueOf(groupData.getSecreteKey())));
    kvmVideoWall.getProperties().add(new Property("pid",
        String.valueOf(groupData.getPId())));
    kvmVideoWall.setDeviceModel(DeviceType.CAESAR_KAITO_VIDEO_WALL.getDeviceTypeId());
    kvmVideoWall.setBannerType(BannerType.BANNER_KAITO);
    List<KvmVideoWallDecoder> decoders = new ArrayList<>();
    for (KaitoVideoWall.Screen screen : kaitoVideoWall.getScreens()) {
      KvmVideoWallDecoder decoder = new KvmVideoWallDecoder();
      decoder.setWidth(screen.getWidth());
      decoder.setHeight(screen.getHeight());
      decoder.setXpos(screen.getX());
      decoder.setYpos(screen.getY());
      decoders.add(decoder);
    }
    kvmVideoWall.setDecoders(decoders);
  }

  @BeforeMapping
  public void toKvmSeatBefore(CaesarSeat caesarSeat,
                              @MappingTarget KvmSeat kvmSeat, @Context String masterId) {
    kvmSeat.setMasterId(masterId);
  }

  /**
   * toKvmMaster.
   */
  @BeforeMapping
  public void toKvmMaster(CaesarServer caesarServer, @MappingTarget KvmMaster kvmMaster) {
    List<Property> properties = new ArrayList<>();
    properties.add(
        new Property(PropertyKeyConst.FAN_NUMBER, String.valueOf(caesarServer.getTotalFanNumber())));
    properties.add(
        new Property(PropertyKeyConst.POWER_NUMBER, String.valueOf(caesarServer.getTotalPowerNumber())));
    properties.add(
        new Property(PropertyKeyConst.PORT_NUMBER, String.valueOf(caesarServer.getTotalPortNumber())));
    properties.add(
        new Property(PropertyKeyConst.SLOT_NUMBER, String.valueOf(caesarServer.getTotalSlotNumber())));
    properties.add(new Property("portPreSlot", String.valueOf(caesarServer.getPortPreSlot())));
    properties.add(new Property("deviceType", caesarServer.getDeviceModel()));
    properties.add(new Property("systemVersion", caesarServer.getSoftVersion()));
    properties.add(new Property("fpgaVersion", caesarServer.getFpgaVersion()));
    kvmMaster.setProperties(properties);
  }

  @BeforeMapping
  public void toKvmUserBefore(CaesarUser caesarUser, @MappingTarget KvmUser kvmUser,
                              @Context String masterId) {
    kvmUser.setMasterId(masterId);
  }

  private void lookupKvmAsset(int caesarVideoSrcId, KvmAssetContext context) {
    if (context.getKvmAsset() != null && context.getKvmAsset().getDeviceId() == caesarVideoSrcId) {
      return;
    }
    KvmAsset kvmAsset = kvmAssetService.oneByDeviceId(caesarVideoSrcId, context.getMasterId());
    context.setKvmAsset(kvmAsset);
  }

  private void lookupKvmAsset(String assetId, KvmAssetContext context) {
    if (context.getKvmAsset() != null
        && Objects.equals(context.getKvmAsset().getAssetId(), assetId)) {
      return;
    }
    KvmAsset kvmAsset = kvmAssetService.getById(assetId);
    context.setKvmAsset(kvmAsset);
  }

  private void initializeCommonAssetData(KvmAsset kvmAsset, CaesarDeviceSameField device, String masterId) {
    kvmAsset.setAssetId(SkyLinkStringUtil.uuid());
    kvmAsset.setMasterId(masterId);
    kvmAsset.setHardcode(InspectCaesarUtil.getCaesarDeviceHardCode(masterId, device.getSn(), device.getId()));
    kvmAsset.resetVersion();
    kvmAsset.getVersion().add(new Version("app", device.getSoftVersion(), ""));
    kvmAsset.getVersion().add(new Version("sys", device.getSystemVersion(), ""));
    kvmAsset.getVersion().add(new Version("fpga", device.getFpgaVersion(), ""));
    addCommonProperties(kvmAsset, device);
    // 通用的属性拷贝
    for (CaesarProperty property : device.getProperties()) {
      kvmAsset.getProperties().add(new Property(property.getKey(), property.getValue()));
    }
  }

  /**
   * 将公共的设备属性添加到 KvmAsset。
   */
  private void addCommonProperties(KvmAsset kvmAsset, CaesarDeviceSameField device) {
    List<Property> properties = kvmAsset.getProperties();
    properties.add(new Property("deviceType", device.getDeviceType()));
    properties.add(new Property("redundant", String.valueOf(device.isRedundant())));
    properties.add(new Property("videoNumber", String.valueOf(device.getVideoNumber())));
    properties.add(new Property(PropertyKeyConst.CAESAR_TX_RESOLUTION_TYPE_KEY, String.valueOf(device.getVideoResolutionType())));
    properties.add(new Property("videoIntfType", String.valueOf(device.getVideoIntfType())));
    properties.add(new Property("link1Port", String.valueOf(device.getLink1Port())));
    properties.add(new Property("link2Port", String.valueOf(device.getLink2Port())));
  }

  /**
   * 处理并设置 Rx 的特殊设备模型。
   */
  private void processAndSetSpecialRxDeviceModel(KvmAsset kvmAsset, CaesarRx caesarRx) {
    caesarRx.getProperties().stream()
            .filter(prop -> Objects.equals(prop.getKey(), PropertyKeyConst.SPECIAL_EXT_TYPE))
            .findFirst() // 找到第一个匹配的即可
            .ifPresent(prop -> {
              DeviceType specialType = InspectCaesarUtil.getSpecialExtType(Integer.parseInt(prop.getValue()));
              kvmAsset.setDeviceModel(specialType.getDeviceTypeId());
            });
  }
}
