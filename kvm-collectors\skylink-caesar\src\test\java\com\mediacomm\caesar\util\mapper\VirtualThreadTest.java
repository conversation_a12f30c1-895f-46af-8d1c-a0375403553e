package com.mediacomm.caesar.util.mapper;

import org.junit.Assert;
import org.junit.Test;

import java.net.URLConnection;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingDeque;

public class VirtualThreadTest implements Runnable {

    private static final LinkedBlockingDeque<Integer> input = new LinkedBlockingDeque<>();
    private static final LinkedBlockingDeque<Integer> output = new LinkedBlockingDeque<>();




    @Test
    public void test() throws InterruptedException {
        List<Thread> threads = new ArrayList<>();
        int count = 100000;
        for (int i = 0; i < count; i++) {
            input.putLast(i);
            threads.add(Thread.ofVirtual().name("test" + i).start(new VirtualThreadTest()));
        }

        for (Thread thread : threads) {
            thread.join();
        }
        for (int i = 0; i < count; i++) {
            System.out.println(i);
            Integer value = output.pollFirst();
            Assert.assertNotNull(value);
            Assert.assertEquals(i, value.intValue());
        }
    }

    @Override
    public void run() {
        Integer value = input.pollFirst();
        if (value != null) {
            try {
                output.putLast(value);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }
}
